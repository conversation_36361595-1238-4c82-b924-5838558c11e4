"use client";
import { useState } from "react";
import Image from "next/image";
import { AppButton } from "../../components/app-button";
import Link from "next/link";
import { FaTelegram } from "react-icons/fa";
import { LiaTimesSolid } from "react-icons/lia";
import { HiOutlineBars3BottomLeft } from "react-icons/hi2";

const PromoNavbar = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);

  const toggleMenu = () => {
    setIsMenuOpen(!isMenuOpen);
  };

  const closeMenu = () => {
    setIsMenuOpen(false);
  };

  const navLinks = [
    { href: "/#benefits", label: "Why" },
    { href: "/#testimonials", label: "Testimonials" },
    { href: "/#trophies", label: "Trophies" },
    { href: "/#team", label: "Team" },
    { href: "/#services", label: "Services" },
    { href: "/#builder", label: "Builder" },
    { href: "/bootcamp", label: "Bootcamp" },
    { href: "https://book.getrecon.xyz/", label: "Docs", external: true },
  ];

  const mobileLinks = navLinks.map((link) =>
    link.href === "/#builder" ? { ...link, label: "Free Builder" } : link
  );

  const telegramBtn = (
    <Link
      href="https://t.me/GalloDaSballo"
      className="m-0 flex flex-row items-center justify-center p-0 text-center"
      target="_blank"
      rel="noopener noreferrer"
    >
      <AppButton variant="secondary" size="lg" rightIcon={<FaTelegram />}>
        Questions? Ask the Founder
      </AppButton>
    </Link>
  );

  const loginBtn = (
    <Link href="/dashboard" className="m-0 w-[150px] p-0 text-center">
      <AppButton variant="outline" size="lg">
        Log in &gt;
      </AppButton>
    </Link>
  );

  return (
    <div className="fixed z-[1000] w-full">
      <div className="flex h-20 w-full items-center justify-between bg-[#E6E0E91F] px-5 py-4 backdrop-blur-md">
        <div className="max-w-[20%]">
          <Link href="/" passHref className="inline list-none px-2.5">
            <Image
              src="/recon-logo.svg"
              alt="Recon Logo"
              width={70}
              height={60}
              priority
            />
          </Link>
        </div>
        <div className="hidden max-w-[80%] flex-row items-center justify-between lg:flex">
          <div className="flex flex-row justify-between px-5 text-white">
            {navLinks.map(({ href, label, external }) => (
              <Link
                key={label}
                href={href}
                passHref
                className="inline list-none px-2.5"
                target={external ? "_blank" : undefined}
                rel={external ? "noopener noreferrer" : undefined}
              >
                {label}
              </Link>
            ))}
          </div>
          <div className="flex flex-row justify-between px-5 text-white">
            {telegramBtn}
            {loginBtn}
          </div>
        </div>
        <div className="flex items-center lg:hidden">
          <button
            onClick={toggleMenu}
            className="text-white focus:outline-none"
          >
            {isMenuOpen ? (
              <LiaTimesSolid className="size-8" />
            ) : (
              <HiOutlineBars3BottomLeft className="size-8" />
            )}
          </button>
        </div>
      </div>

      {isMenuOpen && (
        <div className="fixed inset-0 top-20 z-[999] flex flex-col items-center justify-center bg-black/95 text-white">
          <ul className="flex w-full flex-col items-center space-y-8 text-lg">
            {mobileLinks.map(({ href, label, external }) => (
              <Link
                key={label}
                href={href}
                passHref
                className="sub-title-custom uppercase"
                onClick={closeMenu}
                target={external ? "_blank" : undefined}
                rel={external ? "noopener noreferrer" : undefined}
              >
                {label}
              </Link>
            ))}
          </ul>
          <div className="mt-8 flex w-full flex-col items-center space-y-4">
            {telegramBtn}
          </div>
        </div>
      )}
    </div>
  );
};

export default PromoNavbar;
