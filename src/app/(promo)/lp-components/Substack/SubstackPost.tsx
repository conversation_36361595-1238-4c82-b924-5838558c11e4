import React from "react";
import Image from "next/image";

interface SubstackPostProps {
  title: string;
  description: string;
  img: string;
  link: string;
}

export default function SubstackPost({
  title,
  description,
  img,
  link,
}: SubstackPostProps) {
  return (
    <div className="mr-7 min-h-[272px] w-[300px] flex-none snap-center rounded-lg bg-[#6750A41F] from-purple-800 to-gray-900 p-4 text-white shadow-lg md:w-[370px] lg:w-[370px]">
      <div className="flex h-full flex-col justify-between">
        <p className="text-[24px]">{title}</p>
        <p className="text-[16px]">{description}</p>
        <Image
          src={img}
          alt={"Substack Image"}
          height={160}
          width={300}
          className="w-full object-cover"
        />
        <a
          href={link}
          target="_blank"
          rel="noreferrer"
          className="block text-lg font-semibold text-[#D0BCFF] underline"
        >
          Read &gt;
        </a>
      </div>
    </div>
  );
}
