import React from "react";
import {
  Body2,
  Body1Strong,
  Title2Strong,
} from "../../../components/app-typography";

interface TrophyProps {
  project: string;
  severity: string;
  findingName: string;
  findingUrl: string;
  reconLogs: string;
  description: string;
}

export default function Trophy({
  project,
  severity,
  findingName,
  findingUrl,
  reconLogs,
  description,
}: TrophyProps) {
  return (
    <div className="mr-7 min-h-[272px] w-[300px] flex-none snap-center rounded-lg bg-back-accent-quaternary p-4 shadow-lg md:w-[370px] lg:w-[370px]">
      <div className="flex h-full flex-col justify-between">
        <div className="grid grid-cols-[3fr_1fr] items-start gap-4">
          <div>
            <Title2Strong
              color="primary"
              className="text-left text-fore-on-accent-primary"
            >
              {project}
            </Title2Strong>
            <Body1Strong
              color="primary"
              className="text-left text-fore-on-accent-primary"
            >
              {severity} | {findingName}
            </Body1Strong>
            <a
              href={findingUrl}
              target="_blank"
              rel="noreferrer"
              className="underline"
            >
              <Body1Strong
                color="accent-alt"
                className="text-accent-alt-primary"
              >
                Finding
              </Body1Strong>
            </a>{" "}
            |{" "}
            <a
              href={reconLogs}
              target="_blank"
              rel="noreferrer"
              className="underline"
            >
              <Body1Strong
                color="accent-alt"
                className="text-accent-alt-primary"
              >
                Recon Logs
              </Body1Strong>
            </a>
          </div>
          <div className="flex items-start justify-center">
            <span className="flex size-12 items-center justify-center rounded-full bg-accent-alt-tertiary text-2xl">
              🏆
            </span>
          </div>
        </div>

        <div className="mt-4 flex flex-col justify-center">
          <Body2 color="primary" className="text-fore-on-accent-primary">
            {description}
          </Body2>
          <a
            href={findingUrl}
            target="_blank"
            rel="noreferrer"
            className="block underline"
          >
            <Body1Strong color="accent-alt" className="text-accent-alt-primary">
              BUG LINK &gt;
            </Body1Strong>
          </a>
        </div>
      </div>
    </div>
  );
}
